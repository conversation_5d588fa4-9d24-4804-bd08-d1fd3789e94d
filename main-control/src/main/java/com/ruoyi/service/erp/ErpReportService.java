package com.ruoyi.service.erp;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.uuid.UUID;
import com.ruoyi.domain.basicData.BasicDocumentInfo;
import com.ruoyi.domain.basicData.BasicMaterialInfo;
import com.ruoyi.domain.erp.ErpReportDetail;
import com.ruoyi.domain.erp.ErpReportMain;
import com.ruoyi.mapper.basicData.BasicDocumentInfoMapper;
import com.ruoyi.mapper.erp.ErpReportDetailMapper;
import com.ruoyi.mapper.erp.ErpReportMainMapper;
import com.ruoyi.service.basicData.BasicMaterialInfoService;
import com.ruoyi.utils.DateAndTimeUtil;
import com.ruoyi.utils.constant.CommonConstant;
import com.ruoyi.vo.document.DocumentDetailVo;
import com.ruoyi.vo.document.DocumentInventoryDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ERP上报服务
 *
 * <AUTHOR>
 * @date 2024-12-23
 */
@Slf4j
@Service
public class ErpReportService extends ServiceImpl<ErpReportMainMapper, ErpReportMain> {

    @Resource
    private ErpReportMainMapper erpReportMainMapper;

    @Resource
    private ErpReportDetailMapper erpReportDetailMapper;

    @Resource
    private BasicDocumentInfoMapper basicDocumentInfoMapper;

    @Resource
    private BasicMaterialInfoService basicMaterialInfoService;

    @Resource
    private ErpService erpService;

    /**
     * 上报当前出入库操作到ERP
     *
     * @param lists 本次操作的单据明细列表
     * @param type  操作类型（入库/出库）
     */
    public void reportCurrentOperation(List<DocumentDetailVo> lists, Integer type) {
        log.info("开始创建ERP上报记录，操作类型：{}，物料明细数量：{}", type, lists.size());
        // 按单据编码分组
        Map<String, List<DocumentDetailVo>> documentGroups = lists.stream()
                .collect(Collectors.groupingBy(DocumentDetailVo::getDocumentCode));
        log.info("按单据分组后，单据数量：{}", documentGroups.size());
        for (Map.Entry<String, List<DocumentDetailVo>> entry : documentGroups.entrySet()) {
            String documentCode = entry.getKey();
            List<DocumentDetailVo> documentDetails = entry.getValue();
            try {
                // 获取单据信息
                BasicDocumentInfo documentInfo = basicDocumentInfoMapper.getDocumentInfoByCode(documentCode);
                if (documentInfo == null) {
                    log.warn("未找到单据信息，单据编码：{}", documentCode);
                    continue;
                }
                // 创建上报记录（传入该单据的所有物料明细）
                createReportRecord(documentDetails, documentInfo);

            } catch (Exception e) {
                log.error("创建ERP上报记录失败，单据编码：{}，错误：{}", documentCode, e.getMessage(), e);
            }
        }
    }

    /**
     * 创建上报记录
     *
     * @param documentDetails 同一单据的所有物料明细列表
     * @param documentInfo    单据信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void createReportRecord(List<DocumentDetailVo> documentDetails, BasicDocumentInfo documentInfo) {
        if (documentDetails == null || documentDetails.isEmpty()) {
            log.warn("单据明细列表为空，跳过创建上报记录");
            return;
        }

        String documentCode = documentDetails.get(0).getDocumentCode();
        // 1. 创建主表记录
        ErpReportMain mainRecord = createMainRecord(documentDetails, documentInfo);
        // 2. 创建明细记录
        List<ErpReportDetail> detailRecords = createDetailRecords(documentDetails, mainRecord.getId());
        // 3. 保存到数据库
        erpReportMainMapper.insert(mainRecord);
        if (!detailRecords.isEmpty()) {
            erpReportDetailMapper.batchInsert(detailRecords);
        }
        log.info("ERP上报记录创建成功，上报编号：{}，单据编码：{}，物料数量：{}，明细数量：{}",
                mainRecord.getReportNo(), documentCode, documentDetails.size(), detailRecords.size());

        // 4. 事务提交后再执行异步上报，避免异步方法在事务提交前执行
        String mainId = mainRecord.getId();
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCommit() {
                log.info("事务提交成功，开始异步执行ERP上报，上报编号：{}", mainRecord.getReportNo());
                executeReportAsync(mainId);
            }
            @Override
            public void afterCompletion(int status) {
                if (status == STATUS_ROLLED_BACK) {
                    log.warn("事务回滚，取消ERP上报，上报编号：{}", mainRecord.getReportNo());
                }
            }
        });
    }

    /**
     * 创建主表记录
     */
    private ErpReportMain createMainRecord(List<DocumentDetailVo> documentDetails, BasicDocumentInfo documentInfo) {
        ErpReportMain mainRecord = new ErpReportMain();
        mainRecord.setId(UUID.randomUUID().toString());
        mainRecord.setReportNo(generateReportNo());
        mainRecord.setDocumentCode(documentDetails.get(0).getDocumentCode());
        mainRecord.setBusinessType(documentInfo.getBusinessType());
        mainRecord.setTransactionType(documentInfo.getTransactionType());
        mainRecord.setReportStatus(CommonConstant.ReportStatus.PENDING);
        // 计算所有物料的总数量
        BigDecimal totalQuantity = BigDecimal.ZERO;
        for (DocumentDetailVo detail : documentDetails) {
            if (detail.getDetails() != null) {
                for (DocumentInventoryDetailVo inventoryDetail : detail.getDetails()) {
                    totalQuantity = totalQuantity.add(BigDecimal.valueOf(inventoryDetail.getQuantity()));
                }
            }
        }
        mainRecord.setTotalQuantity(totalQuantity);
        mainRecord.setCreateTime(DateAndTimeUtil.getNowDate());
        mainRecord.setReporter(SecurityUtils.getUsername());

        return mainRecord;
    }

    /**
     * 创建明细记录
     */
    private List<ErpReportDetail> createDetailRecords(List<DocumentDetailVo> documentDetails, String mainId) {
        List<ErpReportDetail> detailRecords = new ArrayList<>();

        // 遍历所有物料
        for (DocumentDetailVo detail : documentDetails) {
            if (detail.getDetails() != null) {
                // 遍历每个物料的库存明细
                for (DocumentInventoryDetailVo inventoryDetail : detail.getDetails()) {
                    ErpReportDetail detailRecord = new ErpReportDetail();
                    detailRecord.setId(UUID.randomUUID().toString());
                    detailRecord.setMainId(mainId);
                    detailRecord.setMaterialCode(detail.getMaterialCode());
                    detailRecord.setContainerCode(inventoryDetail.getContainerCode());
                    detailRecord.setQuantity(BigDecimal.valueOf(inventoryDetail.getQuantity()));
                    detailRecord.setCreateTime(DateAndTimeUtil.getNowDate());
                    // 获取物料信息
                    BasicMaterialInfo materialInfo = basicMaterialInfoService.queryBasicMaterialByCode(detail.getMaterialCode());
                    if (materialInfo != null) {
                        detailRecord.setMaterialName(materialInfo.getMaterialName());
                        detailRecord.setUnit(materialInfo.getProduceUnit());
                    }
                    detailRecords.add(detailRecord);
                }
            }
        }
        return detailRecords;
    }

    /**
     * 生成上报编号
     */
    private String generateReportNo() {
        return "ERP_REPORT_" + System.currentTimeMillis();
    }

    /**
     * 异步执行上报
     */
    @Async
    public void executeReportAsync(String mainId) {
        try {
            executeReport(mainId);
        } catch (Exception e) {
            log.error("异步执行ERP上报失败，主表ID：{}，错误：{}", mainId, e.getMessage(), e);
        }
    }

    /**
     * 执行具体上报逻辑
     */
    private void executeReport(String mainId) {
        ErpReportMain mainRecord = erpReportMainMapper.selectById(mainId);
        if (mainRecord == null) {
            log.warn("未找到上报记录，主表ID：{}", mainId);
            return;
        }

        List<ErpReportDetail> details = erpReportDetailMapper.selectList(
                new LambdaQueryWrapper<ErpReportDetail>()
                        .eq(ErpReportDetail::getMainId, mainId)
                        .orderByAsc(ErpReportDetail::getCreateTime)
        );

        try {
            log.info("开始执行ERP上报，上报编号：{}", mainRecord.getReportNo());
            // 调用ERP接口
            String erpBillNo = callErpInterface(mainRecord, details);
            if (erpBillNo == null || erpBillNo.isEmpty()) {
                log.warn("ERP上报失败（接口返回空），上报编号：{}", mainRecord.getReportNo());
                updateReportStatus(mainId, CommonConstant.ReportStatus.FAILED, null);
                return;
            }
            // 更新成功状态
            updateReportStatus(mainId, CommonConstant.ReportStatus.SUCCESS, erpBillNo);
            log.info("ERP上报成功，上报编号：{}，ERP单据编号：{}", mainRecord.getReportNo(), erpBillNo);
        } catch (Exception e) {
            // 更新失败状态
            updateReportStatus(mainId, CommonConstant.ReportStatus.FAILED, null);
            log.error("ERP上报失败，上报编号：{}，错误：{}", mainRecord.getReportNo(), e.getMessage());
        }
    }

    /**
     * 调用ERP接口
     */
    private String callErpInterface(ErpReportMain mainRecord, List<ErpReportDetail> details) {
        try {
            log.info("开始调用ERP接口，业务类型：{}，单据编码：{}，明细数量：{}",
                    mainRecord.getBusinessType(), mainRecord.getDocumentCode(), details.size());

            // 根据业务类型调用不同的ERP接口
            String erpBillNo = null;

            if (CommonConstant.BoundType.WLDB.equals(mainRecord.getBusinessType())) {
                // 调拨单
                erpBillNo = erpService.reportTransferBill(mainRecord, details);
            } else if (CommonConstant.BoundType.CGRK.equals(mainRecord.getBusinessType()) ||
                       CommonConstant.BoundType.SCRK.equals(mainRecord.getBusinessType())) {
                // 采购入库单、生产入库单等 - 可以调用其他入库单接口
                // TODO: 根据具体需求调用相应的入库接口
                log.info("入库类型单据，业务类型：{}，暂返回模拟编号", mainRecord.getBusinessType());
                erpBillNo = "ERP_IN_" + System.currentTimeMillis();
            } else if (CommonConstant.BoundType.XSCK.equals(mainRecord.getBusinessType())) {
                // 销售出库单 - ERP来源的销售出库单（来自发货通知单）上报为调拨单
                log.info("ERP来源的销售出库单，上报为调拨单，单据编码：{}", mainRecord.getDocumentCode());
                erpBillNo = erpService.reportTransferBill(mainRecord, details);
            } else if (CommonConstant.BoundType.CGTH.equals(mainRecord.getBusinessType())) {
                // TODO: 根据具体需求调用相应的出库接口
                log.info("出库类型单据，业务类型：{}，暂返回模拟编号", mainRecord.getBusinessType());
                erpBillNo = "ERP_OUT_" + System.currentTimeMillis();
            } else {
                // 其他类型单据
                log.info("其他类型单据，业务类型：{}，暂返回模拟编号", mainRecord.getBusinessType());
                erpBillNo = "ERP_OTHER_" + System.currentTimeMillis();
            }

            return erpBillNo;

        } catch (Exception e) {
            log.error("调用ERP接口异常，单据编码：{}，错误：{}", mainRecord.getDocumentCode(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新上报状态（通用方法）
     *
     * @param mainId       主表ID
     * @param reportStatus 上报状态
     * @param erpBillNo    ERP单据编号（可为null）
     */
    private void updateReportStatus(String mainId, Integer reportStatus, String erpBillNo) {
        LambdaUpdateWrapper<ErpReportMain> updateWrapper = new LambdaUpdateWrapper<ErpReportMain>()
                .eq(ErpReportMain::getId, mainId)
                .set(ErpReportMain::getReportStatus, reportStatus)
                .set(ErpReportMain::getReportTime, new Date());

        if (erpBillNo != null && !erpBillNo.isEmpty()) {
            updateWrapper.set(ErpReportMain::getErpBillNo, erpBillNo);
        }

        this.update(updateWrapper);
    }

    /**
     * 根据单据编码查询上报记录
     */
    public List<ErpReportMain> getReportsByDocumentCode(String documentCode) {
        return this.list(new LambdaQueryWrapper<ErpReportMain>()
                .eq(ErpReportMain::getDocumentCode, documentCode)
                .orderByDesc(ErpReportMain::getCreateTime)
        );
    }


    /**
     * 根据主表ID查询明细列表
     */
    public List<ErpReportDetail> getDetailsByMainId(String mainId) {
        return erpReportDetailMapper.selectList(
                new LambdaQueryWrapper<ErpReportDetail>()
                        .eq(ErpReportDetail::getMainId, mainId)
                        .orderByAsc(ErpReportDetail::getCreateTime)
        );
    }


}
